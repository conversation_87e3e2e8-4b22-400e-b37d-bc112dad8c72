{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "9475f668ba85f15a-BOM", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/trips?is_active=eq.true&select=%2A%2Ctrip_images%28%2A%29%2Ctestimonials%28id%2Cname%2Crating%2Ctitle%2Ccontent%2Cimage_url%2Ccreated_at%29&slug=eq.manali", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Thu, 29 May 2025 12:23:22 GMT", "sb-gateway-version": "1", "sb-project-ref": "soaoag<PERSON><PERSON><PERSON>", "server": "cloudflare", "set-cookie": "__cf_bm=LxLbZgrGgRGR3S6jOTuxTKsbeG_RB0GBJjLZDFbO9jw-1748521402-*******-oHOH_DRCRzpAya95zl0fGI4UU4OIJ91wVDScxURSkQvi6i2QSrzc4qTufahFy5kCHgbfC7CbbHDHubR5clIfoc3i7IcTgQHUiteEf8f4L1o; path=/; expires=Thu, 29-May-25 12:53:22 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "22"}, "body": "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", "status": 200, "url": "https://soaoagcuubtzojytoati.supabase.co/rest/v1/trips?select=*%2Ctrip_images%28*%29%2Ctestimonials%28id%2Cname%2Crating%2Ctitle%2Ccontent%2Cimage_url%2Ccreated_at%29&slug=eq.manali&is_active=eq.true"}, "revalidate": 31536000, "tags": []}