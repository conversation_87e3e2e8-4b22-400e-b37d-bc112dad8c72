{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "9475fab17c9dff61-B<PERSON>", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/trips?is_active=eq.true&select=%2A%2Ctrip_images%28%2A%29%2Ctestimonials%28id%2Cname%2Crating%2Ctitle%2Ccontent%2Cimage_url%2Ccreated_at%29&slug=eq.rishi<PERSON>h", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Thu, 29 May 2025 12:26:17 GMT", "sb-gateway-version": "1", "sb-project-ref": "soaoag<PERSON><PERSON><PERSON>", "server": "cloudflare", "set-cookie": "__cf_bm=Jo9JL2kEbiN9jhdYgqEG8VPaIjL8..JFKJNpEmMraZc-1748521577-*******-nSH46VkULYmXDS0yMLFZy2xEQ60nYjWaWiESVI1sVlT6bx.KIAFym8iqqsYGAf7ouOFGot5sYqVO8vlBd8DzRMZ76t99ytJNjQE6bVToyWc; path=/; expires=Thu, 29-May-25 12:56:17 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "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", "status": 200, "url": "https://soaoagcuubtzojytoati.supabase.co/rest/v1/trips?select=*%2Ctrip_images%28*%29%2Ctestimonials%28id%2Cname%2Crating%2Ctitle%2Ccontent%2Cimage_url%2Ccreated_at%29&slug=eq.rishikesh&is_active=eq.true"}, "revalidate": 31536000, "tags": []}