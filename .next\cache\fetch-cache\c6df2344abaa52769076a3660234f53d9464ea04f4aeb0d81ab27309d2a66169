{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "9475f6809af5f15a-BOM", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/trips?is_active=eq.true&select=%2A%2Ctrip_images%28%2A%29%2Ctestimonials%28id%2Cname%2Crating%2Ctitle%2Ccontent%2Cimage_url%2Ccreated_at%29&slug=eq.brigu-lake", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Thu, 29 May 2025 12:23:25 GMT", "sb-gateway-version": "1", "sb-project-ref": "soaoag<PERSON><PERSON><PERSON>", "server": "cloudflare", "set-cookie": "__cf_bm=jRobw5hx2wAZxBqf7V6Eb_lBGZ9tTZiX5bhxoherQBk-1748521405-*******-GyD6Lvz5P3LyTDS9hZme70ekJETASbiLuIDz4DxLc7Rl_p1ESajpah8ZpN7z99CrlirGaeG8bqWGc1q7eZYC9yMYg1t5hMIs71fR.cBf5VI; path=/; expires=Thu, 29-May-25 12:53:25 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "eyJpZCI6IjBhMmE0MDNiLTRmYjctNDBlNi1hYjYwLTNlM2M2ODg1ZTYwNCIsInRpdGxlIjoiQnJpZ3UgTGFrZSIsInNsdWciOiJicmlndS1sYWtlIiwiZGVzY3JpcHRpb24iOiJUaGUgQnJpZ3UgTGFrZSB0cmVrLCBsb2NhdGVkIG5lYXIgTWFuYWxpIGluIEhpbWFjaGFsIFByYWRlc2gsIGlzIGEgc3R1bm5pbmcgYWR2ZW50dXJlIHRoYXQgdGFrZXMgeW91IHRocm91Z2ggbHVzaCBmb3Jlc3RzLCBwaWN0dXJlc3F1ZSBtZWFkb3dzLCBhbmQgYnJlYXRodGFraW5nIG1vdW50YWluIHZpZXdzLiIsImRldGFpbGVkX2Rlc2NyaXB0aW9uIjoiVGhlIEJyaWd1IExha2UgdHJlaywgbG9jYXRlZCBuZWFyIE1hbmFsaSBpbiBIaW1hY2hhbCBQcmFkZXNoLCBpcyBhIHN0dW5uaW5nIGFkdmVudHVyZSB0aGF0IHRha2VzIHlvdSB0aHJvdWdoIGx1c2ggZm9yZXN0cywgcGljdHVyZXNxdWUgbWVhZG93cywgYW5kIGJyZWF0aHRha2luZyBtb3VudGFpbiB2aWV3cy4gU3Bhbm5pbmcgYXBwcm94aW1hdGVseSAyMiBraWxvbWV0ZXJzIG92ZXIgdGhyZWUgZGF5cywgdGhlIHRyZWsgbGVhZHMgeW91IHRvIHRoZSBtZXNtZXJpemluZyBnbGFjaWFsIEJyaWd1IExha2UsIHNpdHVhdGVkIGF0IGFuIGFsdGl0dWRlIG9mIGFib3V0IDQsMjM1IG1ldGVycyAoMTMsOTAwIGZlZXQpLiBLbm93biBmb3IgaXRzIHN0cmlraW5nIGJsdWUgd2F0ZXJzIGFuZCBzZXJlbmUgc3Vycm91bmRpbmdzLCB0aGUgbGFrZSBwcm92aWRlcyBhIHBlcmZlY3QgYmFja2Ryb3AgZm9yIHJlbGF4YXRpb24gYW5kIHBob3RvZ3JhcGh5LiBXaXRoIGEgbW9kZXJhdGUgZGlmZmljdWx0eSBsZXZlbCwgdGhlIHRyZWsgaXMgaWRlYWwgZm9yIG5hdHVyZSBlbnRodXNpYXN0cyBhbmQgb2ZmZXJzIGEgY2hhbmNlIHRvIGV4cGVyaWVuY2UgdGhlIHJpY2ggZmxvcmEgYW5kIGZhdW5hIG9mIHRoZSByZWdpb24uIiwiZGVzdGluYXRpb24iOiJIaW1hY2hhbCBQcmFkZXNoIiwiZHVyYXRpb25fZGF5cyI6OSwibWF4X3BhcnRpY2lwYW50cyI6NTAsIm1pbl9wYXJ0aWNpcGFudHMiOjE1LCJwcmljZV9wZXJfcGVyc29uIjoyODUwMC4wMCwiZGlmZmljdWx0eSI6ImNoYWxsZW5naW5nIiwiaW5jbHVzaW9ucyI6WyIzcmQgQUMgdHJhaW4gZmFyZSBmcm9tIEFobWVkYWJhZCB0byBEZWxoaSIsIkxvY2FsIFRyYXZlbCIsIkFjY29tbW9kYXRpb24gb24gcXVhZCBzaGFyaW5nIGJhc2lzIiwiQWxsIE1lYWxzIiwic2lnaHRzZWVpbmcgYW5kIGFjdGl2aXRpZXMgbGlrZSBSaXZlciBSYWZ0aW5nLCBCcmlndSBMYWtlIFRyZWsgYXMgbWVudGlvbmVkIl0sImV4Y2x1c2lvbnMiOlsiUGVyc29uYWwgRXhwZW5zZXMgc3VjaCBhcyBMYXVuZHJ5LCB0ZWxlcGhvbmUsIHRpcHMsIGdyYXR1aXR5LCBNaW5lcmFsL3NvZnQgZHJpbmtzIEFueSBjb3N0IGFyaXNpbmcgZHVlIHRvIG5hdHVyYWwgY2FsYW1pdGllcyBsaWtlLCBsYW5kc2xpZGVzLCByb2FkIGJsb2NrYWdlLCBwb2xpdGljYWwgZGlzdHVyYmFuY2VzLCBldGMiLCJJbnN1cmFuY2UiLCJBbnl0aGluZyB3aGljaCBpcyBub3QgaW5jbHVkZSBpbiB0aGUgaW5jbHVzaW9uIl0sIml0aW5lcmFyeSI6eyJkYXlfMSI6IHsidGl0bGUiOiAiRGVwYXJ0dXJlIGZyb20gQWhtZWRhYmFkIiwgImRlc2NyaXB0aW9uIjogIkJvYXJkIHlvdXIgbW9ybmluZyB0cmFpbiBmcm9tIEFobWVkYWJhZCBSYWlsd2F5IHN0YXRpb24gdG8gRGVsaGkuIERpbm5lciB3aWxsIGJlIHNlcnZlZCBpbiB0aGUgdHJhaW4uIE5pZ2h0IHdpbGwgYmUgaW4gdGhlIHRyYWluLiJ9LCAiZGF5XzIiOiB7InRpdGxlIjogIkRlbGhpIOKAkyBNYW5hbGkiLCAiZGVzY3JpcHRpb24iOiAiUmVhY2ggRGVsaGkgaW4gdGhlIG1vcm5pbmcgYW5kIGdldCB0cmFuc2ZlcnJlZCB0byBNYW5hbGkuIEJyZWFrZmFzdCwgTHVuY2ggJiBEaW5uZXIgd2lsbCBiZSBzZXJ2ZWQgZW4tcm91dGUuIE5pZ2h0IHdpbGwgYmUgaW4gQnVzLiJ9LCAiZGF5XzMiOiB7InRpdGxlIjogIk1hbmFsaSIsICJkZXNjcmlwdGlvbiI6ICJFeHBlY3RlZCB0byByZWFjaCBNYW5hbGkgZWFybHkgaW4gdGhlIG1vcm5pbmcuIE9uIGFycml2YWwgY2hlY2staW4gdG8gQ2FtcHMgYW5kIHJlc3QgZm9yIGEgd2hpbGUuIExhdGVyIHBvc3QgbHVuY2ggaGVhZCBvdXQgZm9yIGxvY2FsIHNpZ2h0c2VlaW5nLiBWaXNpdCBtYWxsIHJvYWQsIEhhZGltYmEgRGV2aSBUZW1wbGUgZXRjLiBhbmQgYmFjayB0byB0aGUgY2FtcC4gRW5qb3kgZXZlbmluZyBhdCBsZWlzdXJlIGZvbGxvd2VkIGJ5IGRpbm5lci4gTmlnaHQgd2lsbCBiZSBpbiBNYW5hbGkuIn0sICJkYXlfNCI6IHsidGl0bGUiOiAiSm9ua2VyIFRoYXRjaCIsICJkZXNjcmlwdGlvbiI6ICJEZXBhcnQgZm9yIEd1bGFiYSwgZnJvbSBNYW5hbGkuIEFycml2ZSBhbmQgc3RhcnQgeW91ciB0cmVrIHRvIEpvbmtlciBUaGF0Y2guIFRoZSB0b3RhbCB0cmVrIHRpbWUgdGFrZW4gdG8gcmVhY2ggaGVyZSBpcyAxLjIgaG91cnMgb2YgZHJpdmluZyBhbmQgMSBob3VyIGFuZCAzMCBtaW51dGVzIG9mIHRyZWtraW5nLiBUaHJvdWdob3V0IHRoZSBoaWtlIGV4cGxvcmUgdGhlIG1lc21lcml6aW5nIEhpbWFjaGFsIFByYWRlc2ggbW91bnRhaW5zLiBBcnJpdmUgYXQgSm9ua2VyIFRoYXRjaCBhbmQgZXN0YWJsaXNoIHlvdXIgY2FtcC4gQWZ0ZXIgTHVuY2ggYWNjbGltYXRpemF0aW9uIHdhbGsuIE5pZ2h0IHdpbGwgYmUgYXQgdGhlIGNhbXBzaXRlLiJ9LCAiZGF5XzUiOiB7InRpdGxlIjogIlJvbGEgS2hvbGkiLCAiZGVzY3JpcHRpb24iOiAiRWFybHkgbW9ybmluZyBhZnRlciBicmVha2Zhc3QsIGRlcGFydCBmb3IgUm9sYSBLaG9saSwgb24gdGhlIHRyYWlsLCB0aGUgd29vZHMgd2lsbCBiZSBkcmFpbmVkLCByZXZlYWxpbmcgcm9sbGluZyBncmVlbiBtZWFkb3dzLiBUaGUgc2hlcGhlcmRzJyBhcmVhIGlzIHdoZXJlIGNhdHRsZSBncmF6ZXJzIGFyZSBnYXRoZXJlZCBmcm9tIHZpbGxhZ2VzIGluIHRoZSBzdW1tZXIuIFlvdSBjYW4gZmluZCBzb21lIG11ZCBodXRzIGFuZCBjYW1wIHBvbGVzIGRvdHRlZCBhcm91bmQuIExlYXZpbmcgdGhlIG1lYWRvdyBpbiBwbGFjZSwgeW91IG1vdmUgdG93YXJkcyB0aGUgc291dGgsIGFuZCB0aGUgc3VtbWl0IGluIHRoZSBmb3JtIG9mIEhhbnVtYW4gVGliYmEgaXMgcmlzaW5nIGluIHNoYXJwIGZvY3VzIHJpZ2h0IG5vdywgYW5kIGEgYml0IGJlbG93IGl0LCB5b3Ugd2lsbCBzZWUgdGhlIFNldmVuIFNpc3RlcnMgUGVha3MuIEJlbG93LCB5b3Ugd2lsbCBmaW5kIHRoZSBncmVlbiBhcmVhIGluIHRoZSBCZWFzIEt1bmQgVmFsbGV5LiBBcnJpdmUgYXQgUm9sYSBLaG9saSBhbmQgZXN0YWJsaXNoIHlvdXIgY2FtcC4gTmlnaHQgd2lsbCBiZSBhdCB0aGUgY2FtcHNpdGUuIn0sICJkYXlfNiI6IHsidGl0bGUiOiAiQmhyaWd1IExha2UiLCAiZGVzY3JpcHRpb24iOiAiRWFybHkgbW9ybmluZyBhZnRlciBicmVha2Zhc3QsIGRlcGFydCBmb3IgQmhyaWd1IExha2UsIGFzIHlvdSBjbGltYiB0aGUgcmlkZ2UsIHlvdSdsbCBzZWUgYW4gb3ZhbC1zaGFwZWQgZ2xhY2lhbCBsYWtlIG9uIHlvdXIgbGVmdC4gRGVwZW5kaW5nIG9uIHRoZSB0aW1lIG9mIHllYXIsIHRoZSBsYWtlIGNhbiBiZSBjb21wbGV0ZWx5IGNvdmVyZWQgYnkgc25vdyBvciBzdXJyb3VuZGVkIGJ5IGdyYXNzIGFuZCBhbHBpbmUgcGxhbnRzLiBJZiB5b3UncmUgZm9ydHVuYXRlIGVub3VnaCB0byBoYXZlIGEgY2xlYXIgZGF5LCB5b3UnbGwgYmUgdHJlYXRlZCB0byBhbiBleGNlbGxlbnQgcGFub3JhbWljIHZpZXcgb2YgUGlyIFBpbmphbCBhbmQgdGhlIERoYXVsYWRoYXIgbW91bnRhaW4gcmFuZ2VzLiBZb3UnbGwgYWxzbyBiZSBhYmxlIHRvIHNlZSBtYWplc3RpYyBwZWFrcyBsaWtlIEluZHJhc2VuLCBEZW8gVGliYmEsIGFuZCBIYW51bWFuIFRpYmJhLiBBZnRlciBzcGVuZGluZyB0aW1lIGF0IHRoZSBsYWtlLCByZXR1cm4gYmFjayB0byBSb2xhIEtob2xpLiBOaWdodCB3aWxsIGJlIGF0IGNhbXBzaXRlIn0sICJkYXlfNyI6IHsidGl0bGUiOiAiR3VsYWJhIiwgImRlc2NyaXB0aW9uIjogIlRyZWsgdG8gR3VsYWJhLCBhcnJpdmUgYW5kIGdldCB0cmFuc2ZlcnJlZCB0byBNYW5hbGkuIExhdGVyIGluIHRoZSBldmVuaW5nIGdldCB0cmFuc2ZlcnJlZCB0byBEZWxoaS4gTmlnaHQgd2lsbCBiZSBpbiBCdXMuIn0sICJkYXlfOCI6IHsidGl0bGUiOiAiRGVsaGkiLCAiZGVzY3JpcHRpb24iOiAiRXhwZWN0ZWQgdG8gcmVhY2ggRGVsaGkgaW4gdGhlIG1vcm5pbmcuIE9uIGFycml2YWwgZ2V0IHRyYW5zZmVycmVkIHRvIERlbGhpIHJhaWx3YXkgdG8gYm9hcmQgeW91ciB0cmFpbiBiYWNrIHRvIEFobWVkYWJhZC4gTmlnaHQgd2lsbCBiZSBpbiB0cmFpbi50cmFuc2ZlcnJlZCB0byBEZWxoaSByYWlsd2F5IHN0YXRpb24gdG8gYm9hcmQgeW91ciB0cmFpbiB0byBEZWxoaS4gTHVuY2ggd2lsbCBiZSBzZXJ2ZWQgZW4tcm91dGUuIE5pZ2h0IHdpbGwgYmUgaW4gdHJhaW4uIn0sICJkYXlfOSI6IHsidGl0bGUiOiAiUmV0dXJuIEJhY2sgdG8gQWhtZWRhYmFkIiwgImRlc2NyaXB0aW9uIjogIldlIHdpbGwgcmVhY2ggQWhtZWRhYmFkIGluIHRoZSBtb3JuaW5nIGFuZCB0aGUgdHJpcCBlbmRzIHdpdGggZm9uZCBtZW1vcmllcyB0byBjaGVyaXNoIGZvciB5ZWFycyB0byBjb21lLiJ9fSwiZmVhdHVyZWRfaW1hZ2VfdXJsIjoiL2ltYWdlcy90cmlwcy9CUklHVS1MQUtFMi53ZWJwIiwiZ2FsbGVyeV9pbWFnZXMiOlsiL2ltYWdlcy90cmlwcy9CUklHVS1MQUtFMi53ZWJwIiwiL2ltYWdlcy90cmlwcy9iaHJpZ3UtbGFrZTMud2VicCJdLCJpc19hY3RpdmUiOnRydWUsImlzX2ZlYXR1cmVkIjp0cnVlLCJhdmFpbGFibGVfZnJvbSI6IjIwMjUtMDMtMTEiLCJhdmFpbGFibGVfdG8iOiIyMDI1LTAzLTE5IiwiY3JlYXRlZF9hdCI6IjIwMjUtMDUtMjlUMTI6MTI6NDUuMjc1NDQ3KzAwOjAwIiwidXBkYXRlZF9hdCI6IjIwMjUtMDUtMjlUMTI6MTI6NDUuMjc1NDQ3KzAwOjAwIiwiY2F0ZWdvcnkiOiJUcmVra2luZyIsIm1vZGVfb2ZfdHJhdmVsIjoiM3JkIFRJRVIgQUMgVFJBSU4gKEFITUVEQUJBRCDigJMgREVMSEkg4oCTIEFITUVEQUJBRCkgKyBBQyBCVVMgKERFTEhJIOKAkyBNQU5BTEkg4oCTREVMSEkpIiwicGlja3VwX2xvY2F0aW9uIjoiQUhNRURBQkFEIFJMWS4gU1RBVElPTiIsImRyb3BfbG9jYXRpb24iOiJBSE1FREFCQUQgUkxZLiBTVEFUSU9OIiwicHJvcGVydHlfdXNlZCI6IkNhbXBzIiwiYWN0aXZpdGllcyI6WyJFeHBsb3JlIHRoaWNrIHBpbmUgZm9yZXN0cyAmIGFwcGxlIG9yY2hhcmRzIiwiVGVudCBQaXRjaGluZyIsIk5hdmlnYXRpb24iLCJXaWxkIExpZmUgYW5kIE5hdHVyZSBQaG90b2dyYXBoeSIsIlJpdmVyIFJhZnRpbmciLCJMb2NhbCBTaWdodHNlZWluZyIsIkV4cGxvcmUgTmF0dXJlIiwiQm9uZmlyZSJdLCJvcHRpb25hbF9hY3Rpdml0aWVzIjpbXSwiYmVuZWZpdHMiOlsiU3RheSBpbiBDYW1wcyIsIlRyYWluZWQgYW5kIHZlcmlmaWVkIFRlYW0iLCJGaXJzdCBhaWQgdHJhaW5lZCBzdGFmZiB0aHJvdWdob3V0IHRoZSB0cmlwIiwiVHJhaW5lZCBmZW1hbGUgdGVhbSBtZW1iZXJzIHRocm91Z2hvdXQgdGhlIHRyaXAiLCJGcmVzaCBoeWdpZW5pYyBmb29kIGZyb20gdHJhdmVsIGZvb2QgcGFydG5lcnMgZHVyaW5nIHRyYWluIGpvdXJuZXkiLCJObyBuaWdodCBqb3VybmV5IGJ5IHJvYWQiXSwic2FmZXR5X3N1cGVydmlzaW9uIjpbIkxvY2FsIFRyYXZlbDogUHJpdmF0ZSwgd2VsbC1tYWludGFpbmVkIGJ1c2VzIHdpdGggZXhwZXJpZW5jZWQgZHJpdmVycyIsIkFjY29tbW9kYXRpb246IFNhZmUgY2FtcGluZyB3aXRoIHByb3BlciBmYWNpbGl0aWVzIiwiU3VwZXJ2aXNpb246IDE6MjAgUmF0aW8gb2YgUDcgc3RhZmYgdG8gcGFydGljaXBhbnRzIHBsdXMgVGVhbSBNYW5hZ2VyLCBlbnN1cmluZyBjbG9zZSBtb25pdG9yaW5nIGF0IGFsbCB0aW1lcyIsIkZpcnN0IEFpZDogRGVkaWNhdGVkIG1lZGljYWwga2l0IGFuZCBzdGFmZiB0cmFpbmVkIGluIGZpcnN0IGFpZCIsIlRyYXZlbCBJbnN1cmFuY2U6IEFsbCBwYXJ0aWNpcGFudHMgYXJlIGNvdmVyZWQgdW5kZXIgdHJhdmVsIGluc3VyYW5jZSIsIjI0IFggNyBUcmlwIEhlbHBsaW5lIE51bWJlci4gRm9yIGFueSBhc3Npc3RhbmNlIHdlIGFyZSBhdmFpbGFibGUgMjRYNyBvbiArOTEgNzg3ODAwNTUwMCJdLCJ0aGluZ3NfdG9fY2FycnkiOlsiUGhvdG8gSUQoQ29tcHVsc29yeSkiLCJQYWNrIHlvdXIgcmVndWxhciBjb3R0b25zIGxpa2UgNSBULVNoaXJ0cyBhbmQgMiDigJMgMyBKZWFucyBvciB0cmFjaywgMiB3b29sZW5zLCBhbmQgTmlnaHQgZHJlc3MuIChObyBzaG9ydCBvciBzbGVldmVsZXNzIGRyZXNzZXMgYWxsb3dlZCkiLCJUb3dlbCwgdHJla2tpbmcgc2hvZXMsIGNvdHRvbiBzb2Nrcywgc2xpcHBlcnMiLCJ3YXRlciBib3R0bGUgLyBiYWciLCJUb3JjaCwgQ2Fwcywgc2FuaXRhcnkgcmVxdWlzaXRlcyIsInBvbHkgYmFnIHRvIHBhY2sgd2V0IHNvaWxlZCBjbG90aHMgZXRjLiIsIlNsZWVwaW5nIGJhZyBhbmQgd2FybSBjbG90aGVzIGZvciBjYW1waW5nIl0sImF2YWlsYWJsZV9kYXRlcyI6WyIxMXRoIE1hcmNoIC0gMTl0aCBNYXJjaCJdLCJjb21tZXJjaWFsX3ByaWNlIjoyODUwMCwicGF5bWVudF90ZXJtcyI6IjUwJSBvZiB0aGUgY29zdCB0byBiZSBwYWlkIGF0IHRoZSB0aW1lIG9mIGJvb2tpbmcgYW5kIHRoZSByZW1haW5pbmcgNTAlIHNoYWxsIGJlIHBhaWQgMTUgZGF5cyBiZWZvcmUgdGhlIFRyaXAiLCJjYW5jZWxsYXRpb25fcG9saWN5IjpudWxsLCJzcGVjaWFsX25vdGVzIjpbIkFDIHdpbGwgbm90IGJlIGZ1bmN0aW9uYWwgaW4gdGhlIGhpbGxzIiwiUGxlYXNlIG5vdGUgdGhhdCB0aGlzIGlzIGEgdGVudGF0aXZlIGl0aW5lcmFyeSBhbmQgbWF5IGNoYW5nZSBhcyBwZXIgdGhlIG9wZXJhdGlvbmFsIGZlYXNpYmlsaXR5IiwiRmluYWwgaXRpbmVyYXJ5IHNoYWxsIGJlIGdpdmVuIDcgZGF5cyBiZWZvcmUgdGhlIGRheSBvZiBkZXBhcnR1cmUiLCJJZiBUcmFpbiBUaWNrZXRzIGFyZSBub3QgYXZhaWxhYmxlIGZyb20gQWhtZWRhYmFkIHdlIHdpbGwgYm9vayB0aGUgdGlja2V0cyB2aWEgVWRhaXB1ciAvIFZhZG9kYXJhIiwiV2Ugc2hvdWxkIG5vdCBiZSBoZWxkIHJlc3BvbnNpYmxlIGZvciBhbnkgdW5mb3Jlc2VlbiBjb25kaXRpb25zIGxpa2Ugcm9hZCBibG9jaywgc3RyaWtlLCByaW90cywgaWxsbmVzcywgYWNjaWRlbnRzLCAnYmFuZGhzJyBldGMiLCJUaGUgc2lnaHRzZWVpbmcgc2NoZWR1bGUgaXMgc3ViamVjdCB0byBjaGFuZ2UgYXMgcGVyIHRoZSBvcGVyYXRpb25hbCBmZWFzaWJpbGl0eSBhbmQgYWxsIGFjdGl2aXRpZXMgYXJlIHN1YmplY3QgdG8gd2VhdGhlciBjb25kaXRpb24iXSwidHJpcF9pbWFnZXMiOltdLCJ0ZXN0aW1vbmlhbHMiOltdfQ==", "status": 200, "url": "https://soaoagcuubtzojytoati.supabase.co/rest/v1/trips?select=*%2Ctrip_images%28*%29%2Ctestimonials%28id%2Cname%2Crating%2Ctitle%2Ccontent%2Cimage_url%2Ccreated_at%29&slug=eq.brigu-lake&is_active=eq.true"}, "revalidate": 31536000, "tags": []}