{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "9475fa77dc5e3e1b-BOM", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/trips?is_active=eq.true&select=%2A%2Ctrip_images%28%2A%29%2Ctestimonials%28id%2Cname%2Crating%2Ctitle%2Ccontent%2Cimage_url%2Ccreated_at%29&slug=eq.beas-kund-trek", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Thu, 29 May 2025 12:26:08 GMT", "sb-gateway-version": "1", "sb-project-ref": "soaoag<PERSON><PERSON><PERSON>", "server": "cloudflare", "set-cookie": "__cf_bm=K.K3o1z2vkR39M_vYXpEOWYYrluzzeRpxFDn7Wbt3JY-1748521568-*******-vdB2uqLvVz5_2W5EIwMIdKEqAML4_QmwQFfyc2q1.BzvP2KrZGxwert4ee12lflV4m409q4h_RRmy8GgbpvtiKN.G_djnkVv3WWGjGFFttc; path=/; expires=Thu, 29-May-25 12:56:08 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "17"}, "body": "eyJpZCI6IjdjOGZhODA0LWQ2M2EtNDcxMy1iNTliLTY5OTBjMGQzZmM5NyIsInRpdGxlIjoiQmVhcyBLdW5kIFRyZWsiLCJzbHVnIjoiYmVhcy1rdW5kLXRyZWsiLCJkZXNjcmlwdGlvbiI6IkJlYXMgS3VuZCBpcyBvbmUgb2YgdGhlIHZlcnkgZmV3IEhpbWFsYXlhbiB0cmVrcyB0aGF0IGxlYWRzIHRvIGltcHJlc3NpdmUgdmlld3Mgb2YgcHJvbWluZW50IG1vdW50YWlucyB3aXRoaW4ganVzdCB0d28gZGF5cyBvZiB0cmVra2luZy4gUGVyZmVjdCBmb3IgYmVnaW5uZXJzLiIsImRldGFpbGVkX2Rlc2NyaXB0aW9uIjpudWxsLCJkZXN0aW5hdGlvbiI6Ik1hbmFsaSwgSGltYWNoYWwgUHJhZGVzaCIsImR1cmF0aW9uX2RheXMiOjksIm1heF9wYXJ0aWNpcGFudHMiOjMwLCJtaW5fcGFydGljaXBhbnRzIjoxLCJwcmljZV9wZXJfcGVyc29uIjoyOTUwMC4wMCwiZGlmZmljdWx0eSI6Im1vZGVyYXRlIiwiaW5jbHVzaW9ucyI6bnVsbCwiZXhjbHVzaW9ucyI6bnVsbCwiaXRpbmVyYXJ5IjpudWxsLCJmZWF0dXJlZF9pbWFnZV91cmwiOiJodHRwczovL3Bvc2l0aXZlNy5pbi93cC1jb250ZW50L3VwbG9hZHMvMjAyNC8xMS9CRUFTLUtVTkQ0LTEwMjR4NjgyLndlYnAiLCJnYWxsZXJ5X2ltYWdlcyI6bnVsbCwiaXNfYWN0aXZlIjp0cnVlLCJpc19mZWF0dXJlZCI6ZmFsc2UsImF2YWlsYWJsZV9mcm9tIjpudWxsLCJhdmFpbGFibGVfdG8iOm51bGwsImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTI5VDA2OjQ5OjExLjgzODgxMyswMDowMCIsInVwZGF0ZWRfYXQiOiIyMDI1LTA1LTI5VDA2OjQ5OjExLjgzODgxMyswMDowMCIsImNhdGVnb3J5IjpudWxsLCJtb2RlX29mX3RyYXZlbCI6bnVsbCwicGlja3VwX2xvY2F0aW9uIjpudWxsLCJkcm9wX2xvY2F0aW9uIjpudWxsLCJwcm9wZXJ0eV91c2VkIjpudWxsLCJhY3Rpdml0aWVzIjpudWxsLCJvcHRpb25hbF9hY3Rpdml0aWVzIjpudWxsLCJiZW5lZml0cyI6bnVsbCwic2FmZXR5X3N1cGVydmlzaW9uIjpudWxsLCJ0aGluZ3NfdG9fY2FycnkiOm51bGwsImF2YWlsYWJsZV9kYXRlcyI6bnVsbCwiY29tbWVyY2lhbF9wcmljZSI6bnVsbCwicGF5bWVudF90ZXJtcyI6bnVsbCwiY2FuY2VsbGF0aW9uX3BvbGljeSI6bnVsbCwic3BlY2lhbF9ub3RlcyI6bnVsbCwidHJpcF9pbWFnZXMiOltdLCJ0ZXN0aW1vbmlhbHMiOltdfQ==", "status": 200, "url": "https://soaoagcuubtzojytoati.supabase.co/rest/v1/trips?select=*%2Ctrip_images%28*%29%2Ctestimonials%28id%2Cname%2Crating%2Ctitle%2Ccontent%2Cimage_url%2Ccreated_at%29&slug=eq.beas-kund-trek&is_active=eq.true"}, "revalidate": 31536000, "tags": []}