{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "9475fad1d93f6eca-BOM", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/trips?is_active=eq.true&select=%2A%2Ctrip_images%28%2A%29%2Ctestimonials%28id%2Cname%2Crating%2Ctitle%2Ccontent%2Cimage_url%2Ccreated_at%29&slug=eq.tirthan-valley-jibhi", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Thu, 29 May 2025 12:26:22 GMT", "sb-gateway-version": "1", "sb-project-ref": "soaoag<PERSON><PERSON><PERSON>", "server": "cloudflare", "set-cookie": "__cf_bm=vTw7i3gufxDtmjFtz6V7YALRj1ZtW1cDWcBnPh.5sro-1748521582-*******-WUjWPxhqpDZlA9zVpURP4552tgGBoHR9mGgRIfK_uGheosYCZfaesaerunwz6i5GuAd50TUx4e3W.EaE5fur997isYZb8bEcCmM6vKe3zm4; path=/; expires=Thu, 29-May-25 12:56:22 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "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", "status": 200, "url": "https://soaoagcuubtzojytoati.supabase.co/rest/v1/trips?select=*%2Ctrip_images%28*%29%2Ctestimonials%28id%2Cname%2Crating%2Ctitle%2Ccontent%2Cimage_url%2Ccreated_at%29&slug=eq.tirthan-valley-jibhi&is_active=eq.true"}, "revalidate": 31536000, "tags": []}